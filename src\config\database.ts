import Mongoose from 'mongoose'
import { env } from '../utils/env'
import AppError from '../common/errors/AppError'

const isDebug = env('APP_DEBUG', 'true') === 'true'

export const initializeDatabase = async (): Promise<void> => {
  Mongoose.set('debug', isDebug)

  try {
    // Prevent reconnecting to the database
    if (Mongoose.connection.readyState >= 1) {
      console.log('We are already connected.')
      return
    }

    Mongoose.connection.on('connected', () => {
      console.log('Connected to the database.')
    })

    Mongoose.connection.on('error', (err) => {
      console.log('Having issues connecting to the database:', err.message)
    })

    await Mongoose.connect(env('MONGO_URI'), {})
  } catch (err) {
    if (err instanceof Error) {
      console.log('Error connecting to MongoDB:', err.message)
    }
    throw new AppError('Error connecting to MongoDB')
  }
}
